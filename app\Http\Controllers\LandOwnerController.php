<?php

namespace App\Http\Controllers;

use App\Models\LandOwner;
use App\Models\LandOwnerAudit;
use App\Traits\EnvironmentAwareTrait;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;

class LandOwnerController extends Controller
{
    use EnvironmentAwareTrait;
    /**
     * Constructor to apply auth middleware
     */
    public function __construct()
    {
        // Temporarily disable auth for debugging
        // $this->middleware('auth:sanctum');
    }
  
    public function index(Request $request): JsonResponse
    {
        $query = LandOwner::with(['creator', 'updater']);

        // Apply search filter
        if ($request->has('search') && $request->search !== '') {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('father_name', 'like', "%{$search}%")
                  ->orWhere('phone', 'like', "%{$search}%")
                  ->orWhere('nid_number', 'like', "%{$search}%");
            });
        }

        // Sorting
        $sortBy = $request->get('sort_by', 'name');
        $sortOrder = $request->get('sort_order', 'asc');
        $query->orderBy($sortBy, $sortOrder);

        // Pagination
        $perPage = $request->get('per_page', 50);
        $landOwners = $query->paginate($perPage);

        // Fix pagination URLs for current environment
        $data = $this->fixPaginationUrls($landOwners);

        return response()->json([
            'success' => true,
            'data' => $data
        ]);
    }

    /**
     * Store a newly created land owner
     */
    public function store(Request $request): JsonResponse
    {
        try {
            $validated = $request->validate([
                'name' => 'required|string|max:255',
                'father_name' => 'required|string|max:255',
                'mother_name' => 'nullable|string|max:255',
                'address' => 'required|string',
                'phone' => 'nullable|string|max:20',
                'nid_number' => 'nullable|string|max:20|unique:land_owners,nid_number',
                'email' => 'nullable|email|max:255',
                'photo' => 'nullable|image|mimes:jpeg,png,jpg,gif,webp|max:5120', // Accept file upload
                'ownership_percentage' => 'nullable|numeric|min:0|max:100',
                'cash_received' => 'nullable|numeric|min:0',
            ]);

            // Handle photo upload if present
            $photoUrl = null;
            if ($request->hasFile('photo')) {
                try {
                    $photoUrl = $this->handleImageUpload($request->file('photo'));
                } catch (\Exception $e) {
                    throw $e;
                }
            } 

            // Remove photo from validated data and add photoUrl
            unset($validated['photo']);
            $validated['photo'] = $photoUrl;

            $landOwner = LandOwner::create($validated);

            // Create audit record (try to create but don't fail if it errors)
            try {
                $userId = Auth::check() ? Auth::id() : null;
                $landOwner->createAudit('created', null, $validated, $userId, 'Land owner record created');
            } catch (\Exception $auditError) {
                // Log audit error but continue with the operation
                \Log::warning('Failed to create audit record for landowner creation: ' . $auditError->getMessage());
            }

            return response()->json([
                'success' => true,
                'message' => 'Land owner created successfully',
                'data' => $landOwner->load(['creator', 'updater'])
            ], 201);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to create land owner: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Display the specified land owner
     */
    public function show(LandOwner $landOwner): JsonResponse
    {
        return response()->json([
            'success' => true,
            'data' => $landOwner->load(['landAcquisitions', 'creator', 'updater', 'audits.user'])
        ]);
    }

    /**
     * Update the specified land owner
     */
    public function update(Request $request, LandOwner $landOwner): JsonResponse
    {
        try {
            // Store original values for audit
            $originalValues = $landOwner->getOriginal();

            // First, get the basic validation rules
            $rules = [
                'name' => 'sometimes|required|string|max:255',
                'father_name' => 'sometimes|required|string|max:255',
                'mother_name' => 'sometimes|nullable|string|max:255',
                'address' => 'sometimes|required|string',
                'phone' => 'sometimes|nullable|string|max:20',
                'nid_number' => 'sometimes|nullable|string|max:20|unique:land_owners,nid_number,' . $landOwner->id,
                'email' => 'sometimes|nullable|email|max:255',
                'ownership_percentage' => 'sometimes|nullable|numeric|min:0|max:100',
                'cash_received' => 'sometimes|nullable|numeric|min:0',
            ];

            // Handle photo validation separately
            if ($request->hasFile('photo')) {
                // If it's a file upload, validate as image
                $rules['photo'] = 'sometimes|nullable|image|mimes:jpeg,png,jpg,gif,webp|max:5120';
            } elseif ($request->has('photo')) {
                // If photo field exists but is not a file, it should be either null, empty string, or existing URL
                $photoValue = $request->input('photo');
                if ($photoValue !== null && $photoValue !== '' && !is_string($photoValue)) {
                    return response()->json([
                        'success' => false,
                        'message' => 'Invalid photo field format'
                    ], 422);
                }
            }

            $validated = $request->validate($rules);

            // Handle photo upload if present
            if ($request->hasFile('photo')) {
                // Delete old photo if exists
                if ($landOwner->photo) {
                    $this->deleteOldImage($landOwner->photo);
                }
                
                // Upload new photo
                $photoUrl = $this->handleImageUpload($request->file('photo'));
                $validated['photo'] = $photoUrl;
            } else {
                // Handle photo field in JSON requests
                if ($request->has('photo')) {
                    $photoValue = $request->input('photo');
                    
                    if ($photoValue === '' || $photoValue === null) {
                        // User wants to remove the photo
                        if ($landOwner->photo) {
                            $this->deleteOldImage($landOwner->photo);
                        }
                        $validated['photo'] = null;
                    } elseif (is_string($photoValue) && strpos($photoValue, '/landowners/') === 0) {
                        // This is an existing photo URL, keep it unchanged
                        // Don't add it to validated array to avoid overwriting
                        unset($validated['photo']);
                    }
                }
                // If photo field is not in the request at all, keep the existing photo unchanged
            }

            $landOwner->update($validated);

            // Get new values for audit
            $newValues = $landOwner->fresh()->toArray();

            // Create audit record (try to create but don't fail if it errors)
            try {
                $userId = Auth::check() ? Auth::id() : null;
                $landOwner->createAudit('updated', $originalValues, $newValues, $userId, 'Land owner record updated');
            } catch (\Exception $auditError) {
                // Log audit error but continue with the operation
                \Log::warning('Failed to create audit record for landowner update: ' . $auditError->getMessage());
            }

            return response()->json([
                'success' => true,
                'message' => 'Land owner updated successfully',
                'data' => $landOwner->fresh(['creator', 'updater'])
            ]);

        } catch (\Illuminate\Validation\ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $e->errors()
            ], 422);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update land owner: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Remove the specified land owner
     */
    public function destroy($id): JsonResponse
    {
        try {
            $owner = LandOwner::find($id);
            if (!$owner) {
                return response()->json(['success' => false, 'message' => 'Not found'], 404);
            }

            // Store original values for audit
            $originalValues = $owner->toArray();

            // Create audit record before deletion (try to create but don't fail if it errors)
            try {
                $userId = Auth::check() ? Auth::id() : null;
                $owner->createAudit('deleted', $originalValues, null, $userId, 'Land owner record deleted');
            } catch (\Exception $auditError) {
                // Log audit error but continue with the operation
                \Log::warning('Failed to create audit record for landowner deletion: ' . $auditError->getMessage());
            }

            // Delete the associated image file if it exists
            if ($owner->photo) {
                $this->deleteOldImage($owner->photo);
            }

            // Delete the database record
            $owner->delete();

            return response()->json(['success' => true, 'message' => 'Land owner and associated files deleted successfully']);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to delete land owner: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get all land owners for dropdown (simplified response)
     */
    public function dropdown(): JsonResponse
    {
        $landOwners = LandOwner::select('id', 'name', 'father_name')
            ->orderBy('name')
            ->get();

        return response()->json([
            'success' => true,
            'data' => $landOwners
        ]);
    }

    /**
     * Get audit history for a specific land owner
     */
    public function auditHistory($id): JsonResponse
    {
        try {
            $landOwner = LandOwner::findOrFail($id);
            
            $audits = LandOwnerAudit::where('land_owner_id', $id)
                ->with('user:id,name,email')
                ->orderBy('created_at', 'desc')
                ->paginate(20);

            return response()->json([
                'success' => true,
                'data' => [
                    'land_owner' => $landOwner->only(['id', 'name', 'father_name']),
                    'audits' => $audits
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch audit history: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get all audit records (admin only)
     */
    public function allAudits(Request $request): JsonResponse
    {
        try {
            $query = LandOwnerAudit::with(['landOwner:id,name,father_name', 'user:id,name,email']);

            // Filter by action
            if ($request->has('action') && $request->action !== '') {
                $query->where('action', $request->action);
            }

            // Filter by user
            if ($request->has('user_id') && $request->user_id !== '') {
                $query->where('user_id', $request->user_id);
            }

            // Filter by date range
            if ($request->has('date_from') && $request->date_from !== '') {
                $query->whereDate('created_at', '>=', $request->date_from);
            }

            if ($request->has('date_to') && $request->date_to !== '') {
                $query->whereDate('created_at', '<=', $request->date_to);
            }

            $audits = $query->orderBy('created_at', 'desc')->paginate(50);

            return response()->json([
                'success' => true,
                'data' => $audits
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch audit records: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get audit statistics
     */
    public function auditStats(): JsonResponse
    {
        try {
            $stats = [
                'total_records' => LandOwner::count(),
                'created_today' => LandOwner::whereDate('created_at', today())->count(),
                'updated_today' => LandOwnerAudit::where('action', 'updated')->whereDate('created_at', today())->count(),
                'deleted_today' => LandOwnerAudit::where('action', 'deleted')->whereDate('created_at', today())->count(),
                'total_audits' => LandOwnerAudit::count(),
                'recent_activity' => LandOwnerAudit::with(['landOwner:id,name,father_name', 'user:id,name'])
                    ->orderBy('created_at', 'desc')
                    ->limit(10)
                    ->get(),
                'actions_summary' => LandOwnerAudit::selectRaw('action, COUNT(*) as count')
                    ->groupBy('action')
                    ->get(),
                'most_active_users' => LandOwnerAudit::selectRaw('user_id, users.name, COUNT(*) as activity_count')
                    ->join('users', 'land_owner_audits.user_id', '=', 'users.id')
                    ->groupBy('user_id', 'users.name')
                    ->orderBy('activity_count', 'desc')
                    ->limit(5)
                    ->get()
            ];

            return response()->json([
                'success' => true,
                'data' => $stats
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch audit statistics: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Handle image upload for land owner photos
     */
    private function handleImageUpload($file): string
    {
        try {
          

            // Check if file is valid
            if (!$file->isValid()) {
                throw new \Exception('Uploaded file is not valid');
            }

            // Generate unique filename
            $filename = 'landowner_' . time() . '_' . Str::random(10) . '.' . $file->getClientOriginalExtension();
            
       
            
            // Create public/landowners directory if it doesn't exist
            $publicDir = public_path('landowners/photo');
            if (!is_dir($publicDir)) {
              
                mkdir($publicDir, 0755, true);
            }
            
            // Move the uploaded file to public/landowners directory
            $destinationPath = $publicDir . DIRECTORY_SEPARATOR . $filename;
            
            if (move_uploaded_file($file->getPathname(), $destinationPath)) {
            
                
                // Return the public URL path
                $url = '/landowners/photo/' . $filename;
                
                // Verify the file exists
                if (file_exists($destinationPath)) {
                    $fileSize = filesize($destinationPath);
                  
                } else {
                   
                    throw new \Exception('File was not properly stored');
                }
                
                return $url;
            } else {
                throw new \Exception('Failed to move uploaded file to public directory');
            }
            
        } catch (\Exception $e) {
          
            throw $e;
        }
    }

    /**
     * Delete old image file
     */
    private function deleteOldImage(string $imageUrl): void
    {
        try {
            // Extract the filename from URL
            // URL format: /landowners/photo/filename.jpg
            $filename = basename($imageUrl);
            
            // Construct the full file path with proper directory separators
            $filePath = public_path('landowners' . DIRECTORY_SEPARATOR . 'photo' . DIRECTORY_SEPARATOR . $filename);
            
            // Check if file exists before attempting to delete
            if (file_exists($filePath)) {
                // Delete the file
                unlink($filePath);
            }
            
        } catch (\Exception $e) {
            throw $e;
        }
    }

    /**
     * Test endpoint without permissions - for debugging
     */
    public function testIndex(): JsonResponse
    {
        try {
            $landOwners = LandOwner::with(['creator', 'updater'])->limit(5)->get();

            return response()->json([
                'success' => true,
                'message' => 'Test endpoint - no permissions required',
                'count' => $landOwners->count(),
                'data' => $landOwners
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error: ' . $e->getMessage()
            ], 500);
        }
    }
}
