<?php

namespace App\Traits;

use Illuminate\Support\Facades\Auth;

trait AuditableTrait
{
    /**
     * Boot the auditable trait for a model.
     */
    protected static function bootAuditableTrait()
    {
        // Set created_by when creating
        static::creating(function ($model) {
            if (Auth::check()) {
                $model->created_by = Auth::id();
                $model->updated_by = Auth::id();
            } else {
                // For testing purposes, allow creation without auth but log it
                $model->created_by = null;
                $model->updated_by = null;
            }
        });

        // Set updated_by when updating
        static::updating(function ($model) {
            if (Auth::check()) {
                $model->updated_by = Auth::id();
            } else {
                // For testing purposes, allow updates without auth
                $model->updated_by = null;
            }
        });
    }
}
