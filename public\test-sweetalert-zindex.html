<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SweetAlert Z-Index Test</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        button {
            padding: 12px 24px;
            margin: 10px 5px;
            cursor: pointer;
            border: none;
            border-radius: 5px;
            font-size: 16px;
            background-color: #007bff;
            color: white;
        }
        button:hover {
            background-color: #0056b3;
        }
        .danger { background-color: #dc3545; }
        .danger:hover { background-color: #c82333; }
        .success { background-color: #28a745; }
        .success:hover { background-color: #218838; }
        .warning { background-color: #ffc107; color: #212529; }
        .warning:hover { background-color: #e0a800; }
        
        /* High z-index modal overlay for testing */
        .test-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            z-index: 50; /* Same as Dialog components */
            display: flex;
            justify-content: center;
            align-items: center;
        }
        
        .test-modal-content {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
            max-width: 500px;
            width: 90%;
        }
        
        .hidden {
            display: none;
        }
        
        /* SweetAlert2 z-index fixes */
        .swal2-z-index-high {
            z-index: 9999 !important;
        }
        
        .swal2-backdrop-z-index-high {
            z-index: 9998 !important;
        }
    </style>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
</head>
<body>
    <div class="container">
        <h1>SweetAlert2 Z-Index Test</h1>
        <p>This page tests that SweetAlert modals appear above other high z-index elements (like dialogs).</p>
        
        <div class="test-section">
            <h2>Basic Tests</h2>
            <button onclick="showSuccess()">Test Success Alert</button>
            <button onclick="showError()" class="danger">Test Error Alert</button>
            <button onclick="showWarning()" class="warning">Test Warning Alert</button>
            <button onclick="showConfirm()" class="danger">Test Confirm Dialog</button>
        </div>
        
        <div class="test-section">
            <h2>Z-Index Test</h2>
            <p>Click the button below to show a modal with z-index: 50 (same as Dialog components), then test SweetAlert:</p>
            <button onclick="showTestModal()">Show Test Modal (z-index: 50)</button>
        </div>
        
        <div class="test-section">
            <h2>Test Results</h2>
            <div id="results">
                <p>Test SweetAlert from both normal page and from within the test modal.</p>
                <p><strong>Expected behavior:</strong> SweetAlert should always appear above everything else.</p>
            </div>
        </div>
    </div>
    
    <!-- Test Modal -->
    <div id="testModal" class="test-modal hidden">
        <div class="test-modal-content">
            <h3>Test Modal (z-index: 50)</h3>
            <p>This modal has the same z-index as Dialog components in the app.</p>
            <p>Test SweetAlert from here to verify it appears above this modal:</p>
            <button onclick="showSuccess()">Success Alert</button>
            <button onclick="showError()" class="danger">Error Alert</button>
            <button onclick="showConfirm()" class="danger">Confirm Dialog</button>
            <br><br>
            <button onclick="hideTestModal()">Close Modal</button>
        </div>
    </div>

    <script>
        // SweetAlert configurations with high z-index
        const swalConfig = {
            customClass: {
                popup: 'swal2-z-index-high',
                backdrop: 'swal2-backdrop-z-index-high'
            }
        };

        function showSuccess() {
            Swal.fire({
                icon: 'success',
                title: 'Success!',
                text: 'This is a success message',
                confirmButtonColor: '#10b981',
                ...swalConfig
            });
        }

        function showError() {
            Swal.fire({
                icon: 'error',
                title: 'Error!',
                text: 'This is an error message',
                confirmButtonColor: '#ef4444',
                ...swalConfig
            });
        }

        function showWarning() {
            Swal.fire({
                icon: 'warning',
                title: 'Warning!',
                text: 'This is a warning message',
                confirmButtonColor: '#f59e0b',
                ...swalConfig
            });
        }

        function showConfirm() {
            Swal.fire({
                title: 'Are you sure?',
                text: "This action cannot be undone!",
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#ef4444',
                cancelButtonColor: '#6b7280',
                confirmButtonText: 'Yes, delete it!',
                ...swalConfig
            }).then((result) => {
                if (result.isConfirmed) {
                    Swal.fire({
                        title: 'Deleted!',
                        text: 'Your file has been deleted.',
                        icon: 'success',
                        confirmButtonColor: '#10b981',
                        ...swalConfig
                    });
                }
            });
        }

        function showTestModal() {
            document.getElementById('testModal').classList.remove('hidden');
        }

        function hideTestModal() {
            document.getElementById('testModal').classList.add('hidden');
        }

        // Close modal when clicking outside
        document.getElementById('testModal').addEventListener('click', function(e) {
            if (e.target === this) {
                hideTestModal();
            }
        });
    </script>
</body>
</html>
