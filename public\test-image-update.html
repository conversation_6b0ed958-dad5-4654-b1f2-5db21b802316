<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Land Owner Image Update Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 20px; border: 1px solid #ccc; }
        button { margin: 5px; padding: 10px; background: #007bff; color: white; border: none; cursor: pointer; }
        button:hover { background: #0056b3; }
        .result { margin-top: 10px; padding: 10px; background: #f8f9fa; border: 1px solid #dee2e6; }
        .error { background: #f8d7da; border-color: #f5c6cb; }
        .success { background: #d4edda; border-color: #c3e6cb; }
        input, textarea { margin: 5px; padding: 8px; width: 300px; }
        label { display: inline-block; width: 150px; }
        .photo-display { max-width: 200px; margin: 10px 0; }
    </style>
</head>
<body>
    <h1>Land Owner Image Update Test</h1>
    
    <div class="test-section">
        <h3>Test Scenarios for Image Updates</h3>
        <p>This page tests various image update scenarios to verify the fixes work correctly.</p>
        
        <button onclick="getCurrentOwnerWithPhoto()">1. Get Owner with Photo</button>
        <button onclick="updateWithoutChangingPhoto()">2. Update Without Changing Photo</button>
        <button onclick="updateAndRemovePhoto()">3. Update and Remove Photo</button>
        <button onclick="updateWithNewPhoto()">4. Update with New Photo</button>
        
        <div id="testResult" class="result"></div>
    </div>

    <div class="test-section">
        <h3>Current Owner Data</h3>
        <div id="currentOwner"></div>
    </div>

    <script>
        const API_BASE = 'http://localhost/real-estate-management/public/api';
        let currentOwner = null;
        
        function showResult(data, isError = false) {
            const element = document.getElementById('testResult');
            element.className = 'result ' + (isError ? 'error' : 'success');
            element.innerHTML = '<pre>' + JSON.stringify(data, null, 2) + '</pre>';
        }

        function displayCurrentOwner(owner) {
            const element = document.getElementById('currentOwner');
            currentOwner = owner;
            
            let photoDisplay = '';
            if (owner.photo) {
                photoDisplay = `<img src="http://localhost/real-estate-management/public${owner.photo}" class="photo-display" alt="Owner Photo" />`;
            }
            
            element.innerHTML = `
                <h4>Owner ID: ${owner.id}</h4>
                <p><strong>Name:</strong> ${owner.name}</p>
                <p><strong>Photo:</strong> ${owner.photo || 'No photo'}</p>
                ${photoDisplay}
                <p><strong>Updated:</strong> ${owner.updated_at}</p>
            `;
        }

        async function getCurrentOwnerWithPhoto() {
            try {
                // First, get a list of owners with photos
                const response = await fetch(`${API_BASE}/land-owners`);
                const data = await response.json();
                
                if (data.success) {
                    const ownersWithPhotos = data.data.data.filter(owner => owner.photo);
                    
                    if (ownersWithPhotos.length > 0) {
                        const owner = ownersWithPhotos[0];
                        displayCurrentOwner(owner);
                        showResult({ message: 'Found owner with photo', owner: owner });
                    } else {
                        showResult({ error: 'No owners with photos found' }, true);
                    }
                } else {
                    showResult(data, true);
                }
            } catch (error) {
                showResult({ error: error.message }, true);
            }
        }

        async function updateWithoutChangingPhoto() {
            if (!currentOwner) {
                showResult({ error: 'Please get an owner with photo first' }, true);
                return;
            }

            try {
                const updateData = {
                    name: currentOwner.name + ' (Updated)',
                    father_name: currentOwner.father_name,
                    address: currentOwner.address + ' - Updated',
                    phone: currentOwner.phone,
                    photo: currentOwner.photo, // Keep existing photo URL
                    ownership_percentage: '75.5'
                };

                console.log('Sending update data:', updateData);

                const response = await fetch(`${API_BASE}/land-owners/${currentOwner.id}`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    },
                    body: JSON.stringify(updateData)
                });

                const result = await response.json();
                
                if (result.success) {
                    displayCurrentOwner(result.data);
                    showResult({ message: 'Successfully updated without changing photo', result: result });
                } else {
                    showResult(result, true);
                }
            } catch (error) {
                showResult({ error: error.message }, true);
            }
        }

        async function updateAndRemovePhoto() {
            if (!currentOwner) {
                showResult({ error: 'Please get an owner with photo first' }, true);
                return;
            }

            try {
                const updateData = {
                    name: currentOwner.name.replace(' (Updated)', '') + ' (No Photo)',
                    father_name: currentOwner.father_name,
                    address: currentOwner.address + ' - Photo Removed',
                    phone: currentOwner.phone,
                    photo: '', // Empty string to remove photo
                    ownership_percentage: '25.0'
                };

                const response = await fetch(`${API_BASE}/land-owners/${currentOwner.id}`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    },
                    body: JSON.stringify(updateData)
                });

                const result = await response.json();
                
                if (result.success) {
                    displayCurrentOwner(result.data);
                    showResult({ message: 'Successfully removed photo', result: result });
                } else {
                    showResult(result, true);
                }
            } catch (error) {
                showResult({ error: error.message }, true);
            }
        }

        async function updateWithNewPhoto() {
            if (!currentOwner) {
                showResult({ error: 'Please get an owner first' }, true);
                return;
            }

            // Create a file input to let user select a photo
            const fileInput = document.createElement('input');
            fileInput.type = 'file';
            fileInput.accept = 'image/*';
            
            fileInput.onchange = async function() {
                if (fileInput.files.length > 0) {
                    const file = fileInput.files[0];
                    
                    try {
                        const formData = new FormData();
                        formData.append('_method', 'PUT');
                        formData.append('name', currentOwner.name.replace(' (No Photo)', '') + ' (New Photo)');
                        formData.append('father_name', currentOwner.father_name);
                        formData.append('address', currentOwner.address + ' - New Photo');
                        formData.append('phone', currentOwner.phone);
                        formData.append('photo', file);
                        formData.append('ownership_percentage', '85.0');

                        const response = await fetch(`${API_BASE}/land-owners/${currentOwner.id}`, {
                            method: 'POST', // Using POST with _method=PUT for file upload
                            headers: {
                                'Accept': 'application/json'
                            },
                            body: formData
                        });

                        const result = await response.json();
                        
                        if (result.success) {
                            displayCurrentOwner(result.data);
                            showResult({ message: 'Successfully updated with new photo', result: result });
                        } else {
                            showResult(result, true);
                        }
                    } catch (error) {
                        showResult({ error: error.message }, true);
                    }
                }
            };
            
            fileInput.click();
        }

        // Auto-load on page load
        window.onload = function() {
            getCurrentOwnerWithPhoto();
        };
    </script>
</body>
</html>
