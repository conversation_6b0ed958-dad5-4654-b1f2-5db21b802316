<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\LandAcquisitionController;
use App\Http\Controllers\LandOwnerController;
use App\Http\Controllers\RoleController;
use App\Http\Controllers\AuthController;
use App\Http\Controllers\ProductionDiagnosticsController;

// Production diagnostics endpoint (remove after debugging)
Route::get('diagnostics', [ProductionDiagnosticsController::class, 'diagnose']);
Route::get('test-db', function() {
    try {
        $count = \App\Models\LandOwner::count();
        return response()->json(['success' => true, 'landowners_count' => $count]);
    } catch (\Exception $e) {
        return response()->json(['success' => false, 'error' => $e->getMessage()]);
    }
});
Route::get('test-auth', function() {
    return response()->json([
        'success' => true, 
        'authenticated' => auth()->check(),
        'user' => auth()->user(),
        'guard' => auth()->getDefaultDriver()
    ]);
})->middleware('auth:sanctum');





// Public authentication routes
Route::prefix('auth')->group(function () {
    Route::post('register', [AuthController::class, 'register']);
    Route::post('login', [AuthController::class, 'login']);
    
    // Protected authentication routes
    Route::middleware('auth:sanctum')->group(function () {
        Route::post('logout', [AuthController::class, 'logout']);
        Route::post('logout-all', [AuthController::class, 'logoutAll']);
        Route::get('profile', [AuthController::class, 'profile']);
        Route::put('profile', [AuthController::class, 'updateProfile']);
        Route::post('change-password', [AuthController::class, 'changePassword']);
        Route::get('permissions', [AuthController::class, 'permissions']);
        Route::post('refresh-token', [AuthController::class, 'refreshToken']);
    });
});

// Protected API routes - require authentication
Route::middleware('auth:sanctum')->group(function () {
    
    // Test endpoint for debugging - no permissions required
    Route::get('land-owners-test', [LandOwnerController::class, 'testIndex']);
    
    // Temporary bypass for debugging - remove this in production
    Route::get('land-owners-bypass', [LandOwnerController::class, 'index']);
    
    // Land Acquisition routes - with permission checks
    Route::middleware('permission:land-acquisition,read')->group(function () {
        Route::get('land-acquisitions', [LandAcquisitionController::class, 'index']);
        Route::get('land-acquisitions/{landAcquisition}', [LandAcquisitionController::class, 'show']);
        Route::get('land-acquisitions-statistics', [LandAcquisitionController::class, 'statistics']);
    });
    
    Route::middleware('permission:land-acquisition,create')->group(function () {
        Route::post('land-acquisitions', [LandAcquisitionController::class, 'store']);
    });
    
    Route::middleware('permission:land-acquisition,update')->group(function () {
        Route::put('land-acquisitions/{landAcquisition}', [LandAcquisitionController::class, 'update']);
        Route::patch('land-acquisitions/{landAcquisition}', [LandAcquisitionController::class, 'update']);
        Route::post('land-acquisitions/{landAcquisition}', [LandAcquisitionController::class, 'update']); // For file uploads with _method=PUT
    });
    
    Route::middleware('permission:land-acquisition,delete')->group(function () {
        Route::delete('land-acquisitions/{landAcquisition}', [LandAcquisitionController::class, 'destroy']);
    });

    // Role Management routes - with permission checks
    Route::middleware('permission:role,read')->group(function () {
        Route::get('roles', [RoleController::class, 'index']);
        Route::get('roles/{role}', [RoleController::class, 'show']);
        Route::get('roles-statistics', [RoleController::class, 'getStatistics']);
        Route::get('roles-modules', [RoleController::class, 'getAvailableModules']);
    });
    
    Route::middleware('permission:role,create')->group(function () {
        Route::post('roles', [RoleController::class, 'store']);
    });
    
    Route::middleware('permission:role,update')->group(function () {
        Route::put('roles/{role}', [RoleController::class, 'update']);
        Route::patch('roles/{role}', [RoleController::class, 'update']);
        Route::patch('roles-bulk-status', [RoleController::class, 'bulkUpdateStatus']);
    });
    
    Route::middleware('permission:role,delete')->group(function () {
        Route::delete('roles/{role}', [RoleController::class, 'destroy']);
    });

    // Land Owner routes - with authentication (temporarily without permission checks)
    Route::get('land-owners', [LandOwnerController::class, 'index']);
    Route::get('land-owners/{landOwner}', [LandOwnerController::class, 'show']);
    Route::get('land-owners-dropdown', [LandOwnerController::class, 'dropdown']);
    Route::get('land-owners/{id}/audit-history', [LandOwnerController::class, 'auditHistory']);
    Route::get('land-owners-audits', [LandOwnerController::class, 'allAudits']);
    Route::get('land-owners-audit-stats', [LandOwnerController::class, 'auditStats']);
    Route::post('land-owners', [LandOwnerController::class, 'store']);
    Route::put('land-owners/{landOwner}', [LandOwnerController::class, 'update']);
    Route::patch('land-owners/{landOwner}', [LandOwnerController::class, 'update']);
    Route::post('land-owners/{landOwner}', [LandOwnerController::class, 'update']); // For file uploads with _method=PUT
    Route::delete('land-owners/{landOwner}', [LandOwnerController::class, 'destroy']);
});

// Environment-aware API info endpoint
Route::get('info', function() {
    return response()->json([
        'success' => true,
        'environment' => app()->environment(),
        'app_url' => config('app.url'),
        'api_base_url' => config('app.url') . '/api',
        'request_info' => [
            'host' => request()->getHost(),
            'port' => request()->getPort(),
            'scheme' => request()->getScheme(),
            'full_url' => request()->fullUrl(),
        ],
        'cors_origins' => explode(',', env('CORS_ALLOWED_ORIGINS', '')),
        'sanctum_domains' => explode(',', env('SANCTUM_STATEFUL_DOMAINS', '')),
    ]);
});

// Legacy route for compatibility
Route::middleware('auth:sanctum')->get('/user', function (Request $request) {
    return $request->user();
});
