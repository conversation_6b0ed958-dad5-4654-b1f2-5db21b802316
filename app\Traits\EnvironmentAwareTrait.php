<?php

namespace App\Traits;

trait EnvironmentAwareTrait
{
    /**
     * Get the correct base URL based on environment
     */
    protected function getBaseUrl(): string
    {
        $url = config('app.url');
        
        // For local development, ensure we use the correct local URL
        if (app()->environment('local')) {
            // If running on development server, use the server URL
            if (request()->getHost() && request()->getPort()) {
                $scheme = request()->isSecure() ? 'https' : 'http';
                $host = request()->getHost();
                $port = request()->getPort();
                
                // Only add port if it's not standard
                if (($scheme === 'http' && $port != 80) || ($scheme === 'https' && $port != 443)) {
                    return "{$scheme}://{$host}:{$port}";
                }
                return "{$scheme}://{$host}";
            }
        }
        
        return $url;
    }

    /**
     * Fix pagination URLs to use correct environment URL
     */
    protected function fixPaginationUrls($paginatedData): array
    {
        $data = $paginatedData->toArray();
        $baseUrl = $this->getBaseUrl();
        
        // Fix pagination URLs
        $urlFields = ['first_page_url', 'last_page_url', 'next_page_url', 'prev_page_url', 'path'];
        
        foreach ($urlFields as $field) {
            if (isset($data[$field]) && $data[$field]) {
                $data[$field] = $this->replaceBaseUrl($data[$field], $baseUrl);
            }
        }
        
        // Fix links array
        if (isset($data['links'])) {
            foreach ($data['links'] as &$link) {
                if (isset($link['url']) && $link['url']) {
                    $link['url'] = $this->replaceBaseUrl($link['url'], $baseUrl);
                }
            }
        }
        
        return $data;
    }

    /**
     * Replace the base URL in a given URL
     */
    private function replaceBaseUrl(string $url, string $newBaseUrl): string
    {
        $parsedUrl = parse_url($url);
        $parsedNewBase = parse_url($newBaseUrl);
        
        $newUrl = $parsedNewBase['scheme'] . '://' . $parsedNewBase['host'];
        
        if (isset($parsedNewBase['port'])) {
            $newUrl .= ':' . $parsedNewBase['port'];
        }
        
        if (isset($parsedUrl['path'])) {
            $newUrl .= $parsedUrl['path'];
        }
        
        if (isset($parsedUrl['query'])) {
            $newUrl .= '?' . $parsedUrl['query'];
        }
        
        return $newUrl;
    }

    /**
     * Get environment-aware response data
     */
    protected function environmentAwareResponse(array $data): array
    {
        return array_merge($data, [
            'environment' => app()->environment(),
            'base_url' => $this->getBaseUrl(),
            'server_info' => [
                'host' => request()->getHost(),
                'port' => request()->getPort(),
                'scheme' => request()->getScheme(),
            ]
        ]);
    }
}
