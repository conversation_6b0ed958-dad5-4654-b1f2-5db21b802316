<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Land Owner CRUD Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 20px; border: 1px solid #ccc; }
        button { margin: 5px; padding: 10px; background: #007bff; color: white; border: none; cursor: pointer; }
        button:hover { background: #0056b3; }
        .result { margin-top: 10px; padding: 10px; background: #f8f9fa; border: 1px solid #dee2e6; }
        .error { background: #f8d7da; border-color: #f5c6cb; }
        .success { background: #d4edda; border-color: #c3e6cb; }
        input, textarea, select { margin: 5px; padding: 8px; width: 200px; }
        label { display: inline-block; width: 150px; }
    </style>
</head>
<body>
    <h1>Land Owner CRUD Test Suite</h1>
    
    <!-- 1. READ/LIST Operation -->
    <div class="test-section">
        <h3>1. List Land Owners (READ)</h3>
        <button onclick="testList()">Get All Land Owners</button>
        <button onclick="testListWithSearch()">Search Test</button>
        <button onclick="testGetOne()">Get Specific Land Owner (ID: 1)</button>
        <div id="listResult" class="result"></div>
    </div>

    <!-- 2. CREATE Operation -->
    <div class="test-section">
        <h3>2. Create Land Owner</h3>
        <form id="createForm">
            <div><label>Name:</label><input type="text" name="name" value="Test Owner API" required></div>
            <div><label>Father Name:</label><input type="text" name="father_name" value="Test Father" required></div>
            <div><label>Mother Name:</label><input type="text" name="mother_name" value="Test Mother"></div>
            <div><label>Address:</label><textarea name="address" required>Test Address 123</textarea></div>
            <div><label>Phone:</label><input type="text" name="phone" value="+8801234567890"></div>
            <div><label>Email:</label><input type="email" name="email" value="<EMAIL>"></div>
            <div><label>NID Number:</label><input type="text" name="nid_number" value=""></div>
            <div><label>Ownership %:</label><input type="number" name="ownership_percentage" value="25.5" step="0.01"></div>
            <div><label>Cash Received:</label><input type="number" name="cash_received" value="1000000" step="0.01"></div>
            <div><label>Photo:</label><input type="file" name="photo" accept="image/*"></div>
        </form>
        <button onclick="testCreate()">Create Land Owner</button>
        <div id="createResult" class="result"></div>
    </div>

    <!-- 3. UPDATE Operation -->
    <div class="test-section">
        <h3>3. Update Land Owner</h3>
        <div><label>Land Owner ID:</label><input type="number" id="updateId" value="1"></div>
        <form id="updateForm">
            <div><label>Name:</label><input type="text" name="name" value="Updated Name API"></div>
            <div><label>Father Name:</label><input type="text" name="father_name" value="Updated Father"></div>
            <div><label>Address:</label><textarea name="address">Updated Address - Test API</textarea></div>
            <div><label>Phone:</label><input type="text" name="phone" value="+8801234567890-UPDATED"></div>
            <div><label>Ownership %:</label><input type="number" name="ownership_percentage" value="30" step="0.01"></div>
            <div><label>Cash Received:</label><input type="number" name="cash_received" value="2000000" step="0.01"></div>
        </form>
        <button onclick="testUpdate()">Update Land Owner</button>
        <div id="updateResult" class="result"></div>
    </div>

    <!-- 4. DELETE Operation -->
    <div class="test-section">
        <h3>4. Delete Land Owner</h3>
        <div><label>Land Owner ID to Delete:</label><input type="number" id="deleteId" value=""></div>
        <button onclick="testDelete()" style="background: #dc3545;">Delete Land Owner</button>
        <div id="deleteResult" class="result"></div>
    </div>

    <!-- 5. AUDIT Operations -->
    <div class="test-section">
        <h3>5. Audit Trail</h3>
        <button onclick="testAuditHistory()">Get Audit History (ID: 1)</button>
        <button onclick="testAllAudits()">Get All Audits</button>
        <button onclick="testAuditStats()">Get Audit Statistics</button>
        <div id="auditResult" class="result"></div>
    </div>

    <script>
        const API_BASE = 'http://localhost/real-estate-management/public/api';
        
        function showResult(elementId, data, isError = false) {
            const element = document.getElementById(elementId);
            element.className = 'result ' + (isError ? 'error' : 'success');
            element.innerHTML = '<pre>' + JSON.stringify(data, null, 2) + '</pre>';
        }

        // 1. LIST/READ Operations
        async function testList() {
            try {
                const response = await fetch(`${API_BASE}/land-owners`);
                const data = await response.json();
                showResult('listResult', data);
            } catch (error) {
                showResult('listResult', { error: error.message }, true);
            }
        }

        async function testListWithSearch() {
            try {
                const response = await fetch(`${API_BASE}/land-owners?search=Abdul&sort_by=name&sort_order=asc`);
                const data = await response.json();
                showResult('listResult', data);
            } catch (error) {
                showResult('listResult', { error: error.message }, true);
            }
        }

        async function testGetOne() {
            try {
                const response = await fetch(`${API_BASE}/land-owners/1`);
                const data = await response.json();
                showResult('listResult', data);
            } catch (error) {
                showResult('listResult', { error: error.message }, true);
            }
        }

        // 2. CREATE Operation
        async function testCreate() {
            try {
                const form = document.getElementById('createForm');
                const formData = new FormData(form);
                
                // Add unique identifier to avoid conflicts
                const timestamp = Date.now();
                formData.set('email', `test${timestamp}@api.com`);
                formData.set('nid_number', `TEST${timestamp}`);
                
                const response = await fetch(`${API_BASE}/land-owners`, {
                    method: 'POST',
                    body: formData
                });
                const data = await response.json();
                showResult('createResult', data, !data.success);
                
                // If successful, set the ID for update/delete testing
                if (data.success && data.data) {
                    document.getElementById('updateId').value = data.data.id;
                    document.getElementById('deleteId').value = data.data.id;
                }
            } catch (error) {
                showResult('createResult', { error: error.message }, true);
            }
        }

        // 3. UPDATE Operation
        async function testUpdate() {
            try {
                const id = document.getElementById('updateId').value;
                const form = document.getElementById('updateForm');
                const formData = new FormData(form);
                
                // Add unique identifier
                const timestamp = Date.now();
                formData.set('phone', `+8801234567890-UPDATED-${timestamp}`);
                
                const response = await fetch(`${API_BASE}/land-owners/${id}`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(Object.fromEntries(formData))
                });
                const data = await response.json();
                showResult('updateResult', data, !data.success);
            } catch (error) {
                showResult('updateResult', { error: error.message }, true);
            }
        }

        // 4. DELETE Operation
        async function testDelete() {
            const id = document.getElementById('deleteId').value;
            if (!id) {
                showResult('deleteResult', { error: 'Please enter an ID to delete' }, true);
                return;
            }
            
            if (!confirm(`Are you sure you want to delete land owner with ID ${id}?`)) {
                return;
            }
            
            try {
                const response = await fetch(`${API_BASE}/land-owners/${id}`, {
                    method: 'DELETE'
                });
                const data = await response.json();
                showResult('deleteResult', data, !data.success);
            } catch (error) {
                showResult('deleteResult', { error: error.message }, true);
            }
        }

        // 5. AUDIT Operations
        async function testAuditHistory() {
            try {
                const response = await fetch(`${API_BASE}/land-owners/1/audit-history`);
                const data = await response.json();
                showResult('auditResult', data, !data.success);
            } catch (error) {
                showResult('auditResult', { error: error.message }, true);
            }
        }

        async function testAllAudits() {
            try {
                const response = await fetch(`${API_BASE}/land-owners-audits`);
                const data = await response.json();
                showResult('auditResult', data, !data.success);
            } catch (error) {
                showResult('auditResult', { error: error.message }, true);
            }
        }

        async function testAuditStats() {
            try {
                const response = await fetch(`${API_BASE}/land-owners-audit-stats`);
                const data = await response.json();
                showResult('auditResult', data, !data.success);
            } catch (error) {
                showResult('auditResult', { error: error.message }, true);
            }
        }

        // Auto-test on page load
        window.onload = function() {
            console.log('CRUD Test page loaded. You can now test all operations.');
        };
    </script>
</body>
</html>
