@echo off
REM Production Deployment Script for Windows
REM Run this script on your production server

echo 🚀 Starting Production Deployment...

echo 📦 Installing/Updating Dependencies...
composer install --no-dev --optimize-autoloader

echo 🔧 Optimizing Configuration...
php artisan config:cache
php artisan route:cache
php artisan view:cache

echo 📊 Running Database Migrations...
php artisan migrate --force

echo 🌱 Running Database Seeders...
php artisan db:seed --force

echo 🧹 Clearing Application Cache...
php artisan cache:clear
php artisan config:clear

echo ✅ Production Deployment Complete!
echo 🌐 Your application should now be working properly.

pause
