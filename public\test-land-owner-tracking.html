<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Land Owner Tracking Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 800px; margin: 0 auto; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .result { background-color: #f9f9f9; padding: 10px; border-radius: 3px; white-space: pre-wrap; }
        button { padding: 10px 15px; margin: 5px; background: #007cba; color: white; border: none; border-radius: 3px; cursor: pointer; }
        button:hover { background: #005a87; }
        input, select { margin: 5px; padding: 5px; width: 200px; }
        .error { color: red; }
        .success { color: green; }
    </style>
</head>
<body>
    <div class="container">
        <h1>Land Owner Tracking System Test</h1>
        
        <div class="section">
            <h3>Authentication</h3>
            <input type="email" id="email" placeholder="Email" value="<EMAIL>">
            <input type="password" id="password" placeholder="Password" value="password123">
            <button onclick="login()">Login</button>
            <button onclick="checkPermissions()">Check User Permissions</button>
            <div id="authResult" class="result"></div>
        </div>

        <div class="section">
            <h3>Test Land Owners Fetch (Debug)</h3>
            <button onclick="testAPI()">Test API Connection</button>
            <button onclick="testDirectLandOwners()">Test Direct Land Owners</button>
            <button onclick="testLandOwners()">Test Fetch Land Owners (No Permissions)</button>
            <button onclick="fetchLandOwners()">Fetch Land Owners (With Permissions)</button>
            <button onclick="bypassLandOwners()">Bypass Fetch (No Middleware)</button>
            <div id="testResult" class="result"></div>
        </div>

        <div class="section">
            <h3>Create Land Owner (with tracking)</h3>
            <input type="text" id="ownerName" placeholder="Owner Name" value="John Doe">
            <input type="text" id="fatherName" placeholder="Father Name" value="Robert Doe">
            <input type="text" id="address" placeholder="Address" value="123 Main Street">
            <input type="text" id="phone" placeholder="Phone" value="************">
            <button onclick="createLandOwner()">Create Land Owner</button>
            <div id="createResult" class="result"></div>
        </div>

        <div class="section">
            <h3>Update Land Owner (with tracking)</h3>
            <input type="number" id="updateId" placeholder="Land Owner ID">
            <input type="text" id="updateName" placeholder="New Name">
            <button onclick="updateLandOwner()">Update Land Owner</button>
            <div id="updateResult" class="result"></div>
        </div>

        <div class="section">
            <h3>View Audit History</h3>
            <input type="number" id="auditId" placeholder="Land Owner ID">
            <button onclick="getAuditHistory()">Get Audit History</button>
            <div id="auditResult" class="result"></div>
        </div>

        <div class="section">
            <h3>Audit Statistics</h3>
            <button onclick="getAuditStats()">Get Audit Statistics</button>
            <div id="statsResult" class="result"></div>
        </div>

        <div class="section">
            <h3>All Audits</h3>
            <button onclick="getAllAudits()">Get All Audits</button>
            <div id="allAuditsResult" class="result"></div>
        </div>
    </div>

    <script>
        let authToken = '';
        const baseURL = '/api';

        async function makeRequest(endpoint, method = 'GET', data = null) {
            const headers = {
                'Content-Type': 'application/json',
                'Accept': 'application/json'
            };

            if (authToken) {
                headers['Authorization'] = `Bearer ${authToken}`;
            }

            const config = {
                method,
                headers
            };

            if (data) {
                config.body = JSON.stringify(data);
            }

            try {
                console.log(`Making request to: ${baseURL + endpoint}`);
                console.log('Headers:', headers);
                const response = await fetch(baseURL + endpoint, config);
                console.log('Response status:', response.status);
                const result = await response.json();
                console.log('Response data:', result);
                return { success: response.ok, data: result, status: response.status };
            } catch (error) {
                console.error('Request error:', error);
                return { success: false, error: error.message };
            }
        }

        async function login() {
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            
            const result = await makeRequest('/auth/login', 'POST', { email, password });
            
            if (result.success && result.data.success) {
                authToken = result.data.data.token;
                document.getElementById('authResult').innerHTML = `<span class="success">Login successful!</span>\nToken: ${authToken.substring(0, 20)}...`;
            } else {
                document.getElementById('authResult').innerHTML = `<span class="error">Login failed:</span>\n${JSON.stringify(result, null, 2)}`;
            }
        }

        async function testLandOwners() {
            if (!authToken) {
                alert('Please login first');
                return;
            }

            const result = await makeRequest('/land-owners-test');
            document.getElementById('testResult').innerHTML = JSON.stringify(result, null, 2);
        }

        async function fetchLandOwners() {
            if (!authToken) {
                alert('Please login first');
                return;
            }

            const result = await makeRequest('/land-owners');
            document.getElementById('testResult').innerHTML = `Regular endpoint result:\n${JSON.stringify(result, null, 2)}`;
        }

        async function checkPermissions() {
            const result = await fetch('/debug-user');
            const data = await result.json();
            document.getElementById('authResult').innerHTML = `Permissions check:\n${JSON.stringify(data, null, 2)}`;
        }

        async function bypassLandOwners() {
            if (!authToken) {
                alert('Please login first');
                return;
            }

            const result = await makeRequest('/land-owners-bypass');
            document.getElementById('testResult').innerHTML = `Bypass endpoint result:\n${JSON.stringify(result, null, 2)}`;
        }

        async function testAPI() {
            const result = await makeRequest('/test');
            document.getElementById('testResult').innerHTML = `API Test Result:\n${JSON.stringify(result, null, 2)}`;
        }

        async function testDirectLandOwners() {
            const result = await makeRequest('/land-owners-direct');
            document.getElementById('testResult').innerHTML = `Direct Land Owners Result:\n${JSON.stringify(result, null, 2)}`;
        }

        async function createLandOwner() {
            if (!authToken) {
                alert('Please login first');
                return;
            }

            const data = {
                name: document.getElementById('ownerName').value,
                father_name: document.getElementById('fatherName').value,
                address: document.getElementById('address').value,
                phone: document.getElementById('phone').value
            };

            const result = await makeRequest('/land-owners', 'POST', data);
            document.getElementById('createResult').innerHTML = JSON.stringify(result, null, 2);
        }

        async function updateLandOwner() {
            if (!authToken) {
                alert('Please login first');
                return;
            }

            const id = document.getElementById('updateId').value;
            const data = {
                name: document.getElementById('updateName').value
            };

            const result = await makeRequest(`/land-owners/${id}`, 'PUT', data);
            document.getElementById('updateResult').innerHTML = JSON.stringify(result, null, 2);
        }

        async function getAuditHistory() {
            if (!authToken) {
                alert('Please login first');
                return;
            }

            const id = document.getElementById('auditId').value;
            const result = await makeRequest(`/land-owners/${id}/audit-history`);
            document.getElementById('auditResult').innerHTML = JSON.stringify(result, null, 2);
        }

        async function getAuditStats() {
            if (!authToken) {
                alert('Please login first');
                return;
            }

            const result = await makeRequest('/land-owners-audit-stats');
            document.getElementById('statsResult').innerHTML = JSON.stringify(result, null, 2);
        }

        async function getAllAudits() {
            if (!authToken) {
                alert('Please login first');
                return;
            }

            const result = await makeRequest('/land-owners-audits');
            document.getElementById('allAuditsResult').innerHTML = JSON.stringify(result, null, 2);
        }
    </script>
</body>
</html>
