<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use App\Traits\AuditableTrait;

class LandOwner extends Model
{
    use HasFactory, AuditableTrait;

    protected $fillable = [
        'name',
        'father_name',
        'mother_name',
        'address',
        'phone',
        'nid_number',
        'email',
        'photo',
        'ownership_percentage',
        'cash_received',
        'created_by',
        'updated_by',
    ];

    /**
     * Relationship with LandAcquisition
     */
    public function landAcquisitions()
    {
        return $this->hasMany(LandAcquisition::class, 'landOwners_id');
    }

    /**
     * Relationship with User who created this land owner
     */
    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Relationship with User who last updated this land owner
     */
    public function updater()
    {
        return $this->belongsTo(User::class, 'updated_by');
    }

    /**
     * Relationship with audit records
     */
    public function audits()
    {
        return $this->hasMany(LandOwnerAudit::class)->orderBy('created_at', 'desc');
    }

    /**
     * Get full name with father's name
     */
    public function getFullNameAttribute(): string
    {
        return $this->father_name ? "{$this->name} S/O {$this->father_name}" : $this->name;
    }

    /**
     * Get the latest audit record
     */
    public function getLatestAuditAttribute()
    {
        return $this->audits()->first();
    }

    /**
     * Get creation info with user details
     */
    public function getCreationInfoAttribute(): array
    {
        return [
            'created_at' => $this->created_at,
            'created_by' => $this->creator ? [
                'id' => $this->creator->id,
                'name' => $this->creator->name,
                'email' => $this->creator->email,
            ] : null,
        ];
    }

    /**
     * Get last update info with user details
     */
    public function getUpdateInfoAttribute(): array
    {
        return [
            'updated_at' => $this->updated_at,
            'updated_by' => $this->updater ? [
                'id' => $this->updater->id,
                'name' => $this->updater->name,
                'email' => $this->updater->email,
            ] : null,
        ];
    }

    /**
     * Create an audit record for this land owner
     */
    public function createAudit(string $action, array $oldValues = null, array $newValues = null, $userId = null, string $notes = null): LandOwnerAudit
    {
        $changedFields = [];
        
        if ($oldValues && $newValues) {
            foreach ($newValues as $key => $newValue) {
                $oldValue = $oldValues[$key] ?? null;
                if ($oldValue != $newValue) {
                    $changedFields[] = $key;
                }
            }
        }

        // Use provided userId or try to get from auth, or use null
        $auditUserId = $userId ?? (auth()->check() ? auth()->id() : null);
        
        // Get user details if available
        $user = $auditUserId ? User::find($auditUserId) : null;

        return LandOwnerAudit::create([
            'land_owner_id' => $this->id,
            'user_id' => $auditUserId,
            'action' => $action,
            'event_type' => 'manual', // Could be 'automatic', 'system', etc.
            'user_name' => $user ? $user->name : null,
            'user_email' => $user ? $user->email : null,
            'old_values' => $oldValues ? json_encode($oldValues) : null,
            'new_values' => $newValues ? json_encode($newValues) : null,
            'changed_fields' => json_encode($changedFields),
            'ip_address' => request()->ip(),
            'user_agent' => request()->userAgent(),
            'source' => 'web', // Could be 'api', 'web', 'import', etc.
            'description' => $notes ?: "{$action} land owner record for {$this->name}",
            'metadata' => null,
        ]);
    }
}
