# Production CRUD Issues - Troubleshooting Guide

## Common Production Issues & Solutions

### 1. **Database Connection Issues**
**Symptoms:** 500 errors, "Connection refused", "Access denied"

**Check:**
```bash
# Test database connection
curl https://yourdomain.com/api/test-db
```

**Common Causes:**
- Wrong database credentials in .env
- Database server not running
- Firewall blocking database port
- Wrong database host (localhost vs 127.0.0.1 vs external IP)

**Solutions:**
```bash
# Update .env with correct credentials
DB_HOST=localhost  # or your database server IP
DB_DATABASE=your_actual_database_name
DB_USERNAME=your_actual_username
DB_PASSWORD=your_actual_password
```

### 2. **File Permissions Issues**
**Symptoms:** "Permission denied", file upload fails

**Check:**
```bash
# Check current permissions
ls -la storage/
ls -la bootstrap/cache/
ls -la public/landowners/
```

**Fix:**
```bash
# Set correct permissions
sudo chown -R www-data:www-data /path/to/your/project
sudo chmod -R 755 /path/to/your/project
sudo chmod -R 775 storage bootstrap/cache public/landowners
```

### 3. **Environment Configuration Issues**
**Symptoms:** Debug info leaking, wrong URLs, CORS errors

**Check .env file:**
```bash
APP_ENV=production
APP_DEBUG=false
APP_URL=https://yourdomain.com  # Must match your actual domain
```

### 4. **CORS Issues**
**Symptoms:** "CORS policy" errors in browser console

**Update .env:**
```bash
SANCTUM_STATEFUL_DOMAINS=yourdomain.com,www.yourdomain.com
SESSION_DOMAIN=.yourdomain.com
```

### 5. **Cache Issues**
**Symptoms:** Old configuration persisting, routes not working

**Clear caches:**
```bash
php artisan cache:clear
php artisan config:clear
php artisan route:clear
php artisan view:clear
```

### 6. **SSL/HTTPS Issues**
**Symptoms:** Mixed content errors, authentication fails

**Force HTTPS in AppServiceProvider:**
```php
// In app/Providers/AppServiceProvider.php boot() method
if (app()->environment('production')) {
    \URL::forceScheme('https');
}
```

### 7. **Authentication Issues**
**Symptoms:** 401 Unauthorized errors, tokens not working

**Check:**
```bash
# Test authentication
curl -H "Authorization: Bearer YOUR_TOKEN" https://yourdomain.com/api/test-auth
```

**Common fixes:**
- Ensure APP_KEY is set correctly
- Check session configuration
- Verify Sanctum stateful domains

### 8. **Server Configuration Issues**

**Apache .htaccess (in public folder):**
```apache
<IfModule mod_rewrite.c>
    RewriteEngine On
    RewriteCond %{REQUEST_FILENAME} !-d
    RewriteCond %{REQUEST_FILENAME} !-f
    RewriteRule ^ index.php [L]
</IfModule>

# Handle CORS preflight requests
<IfModule mod_headers.c>
    Header always set Access-Control-Allow-Origin "*"
    Header always set Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS"
    Header always set Access-Control-Allow-Headers "Content-Type, Authorization, X-Requested-With"
    Header always set Access-Control-Allow-Credentials "true"
</IfModule>
```

**Nginx configuration:**
```nginx
location /api {
    try_files $uri $uri/ /index.php?$query_string;
    
    # Handle preflight requests
    if ($request_method = 'OPTIONS') {
        add_header 'Access-Control-Allow-Origin' '*' always;
        add_header 'Access-Control-Allow-Methods' 'GET, POST, PUT, DELETE, OPTIONS' always;
        add_header 'Access-Control-Allow-Headers' 'Content-Type, Authorization, X-Requested-With' always;
        add_header 'Access-Control-Allow-Credentials' 'true' always;
        add_header 'Content-Length' 0;
        return 204;
    }
}
```

### 9. **PHP Configuration Issues**

**Check php.ini settings:**
```ini
upload_max_filesize = 10M
post_max_size = 10M
max_execution_time = 300
memory_limit = 256M
file_uploads = On
```

### 10. **Migration Issues**
**Symptoms:** Table doesn't exist errors

**Run migrations:**
```bash
php artisan migrate --force
php artisan db:seed --force
```

## Quick Diagnostic Commands

Run these on your production server:

```bash
# 1. Check app status
curl https://yourdomain.com/api/diagnostics

# 2. Test database
curl https://yourdomain.com/api/test-db

# 3. Test landowner endpoint (should get 401 without auth)
curl https://yourdomain.com/api/land-owners

# 4. Check Laravel logs
tail -f storage/logs/laravel.log

# 5. Check server error logs
tail -f /var/log/apache2/error.log  # Apache
tail -f /var/log/nginx/error.log    # Nginx
```

## Specific Error Messages & Solutions

### "Class 'App\Models\LandOwner' not found"
```bash
composer dump-autoload
php artisan optimize:clear
```

### "SQLSTATE[42S02]: Base table or view already exists"
```bash
php artisan migrate:status
php artisan migrate --force
```

### "419 Page Expired" or "CSRF token mismatch"
- Check CORS configuration
- Verify APP_URL matches your domain
- Clear browser cookies

### "500 Internal Server Error"
- Check storage/logs/laravel.log
- Enable temporary debugging: APP_DEBUG=true
- Check file permissions
- Verify database connection
