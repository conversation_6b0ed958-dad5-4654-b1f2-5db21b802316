import React, { useState, useRef } from 'react';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Camera, Upload, X, Eye } from 'lucide-react';

const SimpleImageUpload = ({ 
  value, 
  onChange, 
  label = "Photo", 
  accept = "image/*",
  maxSize = 5 * 1024 * 1024, // 5MB default
  placeholder = "Upload an image",
  className = "",
  disabled = false
}) => {
  const [preview, setPreview] = useState(value || null);
  const [selectedFile, setSelectedFile] = useState(null);
  const [dragOver, setDragOver] = useState(false);
  const fileInputRef = useRef(null);

  // Helper function to get full image URL
  const getImageUrl = (photoPath) => {
    if (!photoPath) return null;
    
    // If it's already a full URL, return as is
    if (photoPath.startsWith('http://') || photoPath.startsWith('https://')) {
      return photoPath;
    }
    
    // If it starts with /landowners, use the base URL
    if (photoPath.startsWith('/landowners/')) {
      return `${window.location.protocol}//${window.location.host}${photoPath}`;
    }
    
    // If it starts with /storage, use the base URL
    if (photoPath.startsWith('/storage/')) {
      return `${window.location.protocol}//${window.location.host}${photoPath}`;
    }
    
    // If it's just a filename or relative path, assume it's in landowners directory
    return `${window.location.protocol}//${window.location.host}/landowners/photo/${photoPath}`;
  };

  const handleFileSelect = (file) => {
    if (!file) return;

    console.log('📁 SimpleImageUpload: File selected', {
      name: file.name,
      size: file.size,
      type: file.type,
      lastModified: file.lastModified
    });

    // Validate file type
    if (!file.type.startsWith('image/')) {
      console.error('❌ Invalid file type:', file.type);
      alert('Please select a valid image file');
      return;
    }

    // Validate file size
    if (file.size > maxSize) {
      console.error('❌ File too large:', file.size, 'bytes, max:', maxSize);
      alert(`File size must be less than ${Math.round(maxSize / (1024 * 1024))}MB`);
      return;
    }

    console.log('✅ File validation passed, creating preview...');

    // Create preview
    const reader = new FileReader();
    reader.onload = (e) => {
      console.log('📷 Preview created successfully');
      setPreview(e.target.result);
    };
    reader.onerror = (e) => {
      console.error('❌ Error creating preview:', e);
    };
    reader.readAsDataURL(file);

    // Store the file for form submission
    setSelectedFile(file);
    console.log('📤 Calling onChange with file object');
    onChange(file); // Pass the file object to parent
  };

  const handleFileChange = (event) => {
    const file = event.target.files[0];
    if (file) {
      handleFileSelect(file);
    }
  };

  const handleDragOver = (e) => {
    e.preventDefault();
    if (!disabled) {
      setDragOver(true);
    }
  };

  const handleDragLeave = (e) => {
    e.preventDefault();
    setDragOver(false);
  };

  const handleDrop = (e) => {
    e.preventDefault();
    setDragOver(false);
    
    if (disabled) return;

    const files = e.dataTransfer.files;
    if (files.length > 0) {
      handleFileSelect(files[0]);
    }
  };

  const handleRemove = () => {
    console.log('🗑️ SimpleImageUpload: Removing image');
    setPreview(null);
    setSelectedFile(null);
    onChange(''); // Send empty string instead of null to clear the field
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const handleClick = () => {
    if (!disabled && fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  // If value is a URL (existing image), show it
  React.useEffect(() => {
    console.log('🔄 SimpleImageUpload: Value changed', {
      value,
      valueType: typeof value,
      isFile: value instanceof File,
      isString: typeof value === 'string'
    });

    if (value && typeof value === 'string') {
      const fullImageUrl = getImageUrl(value);
      console.log('🖼️ Setting preview from URL:', fullImageUrl);
      setPreview(fullImageUrl);
      setSelectedFile(null); // Clear any selected file when showing existing image
    } else if (value === null || value === '') {
      console.log('🗑️ Clearing preview (value is null/empty)');
      setPreview(null);
      setSelectedFile(null);
    } else if (value instanceof File) {
      console.log('📁 Value is a File object, keeping current preview');
      // Don't change preview if value is a File object (user just selected a file)
    }
  }, [value]);

  return (
    <div className={`space-y-2 ${className}`}>
      <Label>{label}</Label>
      
      <div className="space-y-3">
        {/* Upload Area */}
        <div
          className={`
            border-2 border-dashed rounded-lg p-6 text-center cursor-pointer transition-colors
            ${dragOver ? 'border-blue-500 bg-blue-50' : 'border-gray-300'}
            ${disabled ? 'opacity-50 cursor-not-allowed' : 'hover:border-gray-400'}
            ${preview ? 'border-green-300 bg-green-50' : ''}
          `}
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
          onDrop={handleDrop}
          onClick={handleClick}
        >
          <input
            ref={fileInputRef}
            type="file"
            accept={accept}
            onChange={handleFileChange}
            className="hidden"
            disabled={disabled}
          />
          
          {preview ? (
            <div className="flex flex-col items-center space-y-2">
              <Camera className="h-8 w-8 text-green-600" />
              <p className="text-sm text-green-600 font-medium">Image selected</p>
              <p className="text-xs text-gray-500">Click to change or drag a new image</p>
            </div>
          ) : (
            <div className="flex flex-col items-center space-y-2">
              <Upload className="h-8 w-8 text-gray-400" />
              <p className="text-sm text-gray-600">{placeholder}</p>
              <p className="text-xs text-gray-500">
                Drag and drop or click to browse
              </p>
            </div>
          )}
        </div>

        {/* Preview and Actions */}
        {preview && (
          <div className="relative">
            <div className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
              <img
                src={getImageUrl(preview)}
                alt="Preview"
                className="w-16 h-16 object-cover rounded-lg border"
              />
              <div className="flex-1 min-w-0">
                <p className="text-sm font-medium text-gray-900 truncate">
                  {selectedFile ? 'New image selected' : 'Current image'}
                </p>
                <p className="text-xs text-gray-500">
                  {selectedFile ? 'Ready to upload' : 'Uploaded'}
                </p>
              </div>
              <div className="flex space-x-1">
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={(e) => {
                    e.stopPropagation();
                    window.open(getImageUrl(preview), '_blank');
                  }}
                  disabled={disabled}
                >
                  <Eye className="h-4 w-4" />
                </Button>
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={(e) => {
                    e.stopPropagation();
                    handleRemove();
                  }}
                  disabled={disabled}
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default SimpleImageUpload;
