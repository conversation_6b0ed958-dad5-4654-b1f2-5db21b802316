<!DOCTYPE html>
<html>
<head>
    <title>Test Land Owner with Image</title>
</head>
<body>
    <h2>Test Land Owner Creation with Image</h2>
    <form id="landOwnerForm" enctype="multipart/form-data">
        <div>
            <label>Name:</label>
            <input type="text" name="name" value="Test Image Owner" required>
        </div>
        <div>
            <label>Father Name:</label>
            <input type="text" name="father_name" value="Test Image Father" required>
        </div>
        <div>
            <label>Address:</label>
            <input type="text" name="address" value="Test Image Address" required>
        </div>
        <div>
            <label>Phone:</label>
            <input type="text" name="phone" value="123-456-IMAGE">
        </div>
        <div>
            <label>Photo:</label>
            <input type="file" name="photo" accept="image/*">
        </div>
        <div>
            <label>Ownership %:</label>
            <input type="number" name="ownership_percentage" value="40" step="0.01">
        </div>
        <div>
            <label>Cash Received:</label>
            <input type="number" name="cash_received" value="100000" step="0.01">
        </div>
        <button type="submit">Create Land Owner</button>
    </form>

    <div id="result"></div>

    <script>
        document.getElementById('landOwnerForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            const resultDiv = document.getElementById('result');
            
            try {
                const response = await fetch('http://localhost/real-estate-management/public/api/land-owners', {
                    method: 'POST',
                    body: formData
                });
                
                const data = await response.json();
                resultDiv.innerHTML = '<pre>' + JSON.stringify(data, null, 2) + '</pre>';
                
                if (data.success) {
                    resultDiv.style.color = 'green';
                } else {
                    resultDiv.style.color = 'red';
                }
            } catch (error) {
                resultDiv.innerHTML = 'Error: ' + error.message;
                resultDiv.style.color = 'red';
            }
        });
    </script>
</body>
</html>
