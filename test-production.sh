#!/bin/bash
# Production Environment Test Script
# Run this script on your production server after deployment

echo "🧪 Testing Real Estate Management System - Production Environment"
echo "=============================================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Your production domain (update this)
DOMAIN="https://yourdomain.com"
API_URL="${DOMAIN}/api"

echo -e "${YELLOW}Testing API endpoints on: ${DOMAIN}${NC}"
echo ""

# Test 1: API Info
echo "1. Testing API Info endpoint..."
response=$(curl -s -w "%{http_code}" -o /tmp/api_info "${API_URL}/info")
if [ "$response" = "200" ]; then
    echo -e "${GREEN}✅ API Info: SUCCESS${NC}"
    cat /tmp/api_info | jq '.' 2>/dev/null || cat /tmp/api_info
else
    echo -e "${RED}❌ API Info: FAILED (HTTP $response)${NC}"
fi
echo ""

# Test 2: Database Test
echo "2. Testing Database connection..."
response=$(curl -s -w "%{http_code}" -o /tmp/db_test "${API_URL}/test-db")
if [ "$response" = "200" ]; then
    echo -e "${GREEN}✅ Database: SUCCESS${NC}"
    cat /tmp/db_test | jq '.' 2>/dev/null || cat /tmp/db_test
else
    echo -e "${RED}❌ Database: FAILED (HTTP $response)${NC}"
fi
echo ""

# Test 3: Login Test
echo "3. Testing Authentication..."
login_response=$(curl -s -X POST "${API_URL}/auth/login" \
    -H "Content-Type: application/json" \
    -d '{"email":"<EMAIL>","password":"password123"}')

if echo "$login_response" | grep -q "Login successful"; then
    echo -e "${GREEN}✅ Authentication: SUCCESS${NC}"
    echo "$login_response" | jq '.' 2>/dev/null || echo "$login_response"
    
    # Extract token for further tests
    token=$(echo "$login_response" | jq -r '.data.token' 2>/dev/null)
    
    if [ "$token" != "null" ] && [ "$token" != "" ]; then
        echo ""
        echo "4. Testing Protected API with token..."
        
        # Test Land Owners API
        protected_response=$(curl -s -w "%{http_code}" -o /tmp/landowners_test \
            -H "Authorization: Bearer $token" \
            -H "Accept: application/json" \
            "${API_URL}/land-owners")
        
        if [ "$protected_response" = "200" ]; then
            echo -e "${GREEN}✅ Protected API: SUCCESS${NC}"
            echo "Sample response:"
            cat /tmp/landowners_test | jq '.data | {current_page, first_page_url, path}' 2>/dev/null || head -3 /tmp/landowners_test
        else
            echo -e "${RED}❌ Protected API: FAILED (HTTP $protected_response)${NC}"
        fi
    fi
else
    echo -e "${RED}❌ Authentication: FAILED${NC}"
    echo "$login_response"
fi

echo ""
echo "=============================================================="
echo -e "${YELLOW}Test Summary:${NC}"
echo "- API Info endpoint: Check if environment-aware URLs work"
echo "- Database connection: Verify database connectivity"  
echo "- Authentication: Test login functionality"
echo "- Protected API: Test token-based access"
echo ""
echo -e "${YELLOW}Expected Results for Production:${NC}"
echo "- environment: 'production'"
echo "- app_url: '$DOMAIN'"
echo "- api_base_url: '$API_URL'"
echo "- All pagination URLs should use: '$DOMAIN'"
echo ""

# Cleanup
rm -f /tmp/api_info /tmp/db_test /tmp/landowners_test

echo "🔧 Next steps if tests fail:"
echo "1. Check .env file has correct settings"
echo "2. Verify database credentials"
echo "3. Check file permissions (775 for storage/)"
echo "4. Review Laravel logs in storage/logs/"
echo "5. Ensure web server is configured properly"
