<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Only create table if it doesn't exist
        if (!Schema::hasTable('land_owner_audits')) {
            Schema::create('land_owner_audits', function (Blueprint $table) {
                $table->id();
                $table->unsignedBigInteger('land_owner_id');
                $table->unsignedBigInteger('user_id')->nullable(); // Who made the change
                $table->string('action'); // 'created', 'updated', 'deleted'
                $table->json('old_values')->nullable(); // Previous values (for updates)
                $table->json('new_values')->nullable(); // New values
                $table->json('changed_fields')->nullable(); // Fields that were changed
                $table->string('ip_address')->nullable(); // User's IP address
                $table->text('user_agent')->nullable(); // User's browser/agent
                $table->text('notes')->nullable(); // Optional notes
                $table->timestamps();

                // Foreign key constraints
                $table->foreign('land_owner_id')->references('id')->on('land_owners')->onDelete('cascade');
                $table->foreign('user_id')->references('id')->on('users')->onDelete('set null');

                // Indexes for better query performance
                $table->index(['land_owner_id']);
                $table->index(['user_id']);
                $table->index(['action']);
                $table->index(['created_at']);
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('land_owner_audits');
    }
};
