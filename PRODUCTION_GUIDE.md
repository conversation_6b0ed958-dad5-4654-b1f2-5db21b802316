# Production Deployment Troubleshooting Guide

## Common Issues and Solutions

### 1. **CORS Errors**
If you get CORS errors when accessing the API:

**Problem**: Cross-Origin Resource Sharing issues
**Solution**: 
- Update `.env` file with correct domain:
  ```
  APP_URL=https://yourdomain.com
  SANCTUM_STATEFUL_DOMAINS=yourdomain.com,www.yourdomain.com
  SESSION_DOMAIN=.yourdomain.com
  ```
- Run: `php artisan config:cache`

### 2. **Database Connection Errors**
If you get database connection errors:

**Problem**: Database credentials or host issues
**Solution**:
- Update database settings in `.env`:
  ```
  DB_HOST=localhost (or your DB host)
  DB_DATABASE=your_production_database
  DB_USERNAME=your_production_username  
  DB_PASSWORD=your_production_password
  ```
- Test connection: `php artisan migrate:status`

### 3. **File Upload Errors**
If image uploads fail:

**Problem**: File permissions or storage issues
**Solution**:
- Set permissions: `chmod -R 775 storage public/landowners`
- Create storage link: `php artisan storage:link`
- Check disk space: `df -h`

### 4. **Authentication Errors**
If login/auth fails:

**Problem**: Session/Sanctum configuration
**Solution**:
- Update session domain in `.env`:
  ```
  SESSION_DOMAIN=.yourdomain.com
  SANCTUM_STATEFUL_DOMAINS=yourdomain.com
  ```
- Clear config: `php artisan config:cache`

### 5. **500 Internal Server Error**
If you get generic 500 errors:

**Problem**: Various issues, check logs
**Solution**:
- Check Laravel logs: `tail -f storage/logs/laravel.log`
- Check server error logs: `tail -f /var/log/apache2/error.log`
- Set debug temporarily: `APP_DEBUG=true` (remember to turn off!)

### 6. **Route Not Found (404)**
If API routes return 404:

**Problem**: Routes not cached or .htaccess issues
**Solution**:
- Cache routes: `php artisan route:cache`
- Check .htaccess exists in public folder
- Verify server supports URL rewriting

### 7. **Permission Denied Errors**
If you get permission errors:

**Problem**: File/folder permissions
**Solution**:
```bash
sudo chown -R www-data:www-data /path/to/project
sudo chmod -R 755 /path/to/project
sudo chmod -R 775 storage bootstrap/cache public/landowners
```

### 8. **Audit Trail Not Working**
If audit records aren't created:

**Problem**: Authentication or database issues
**Solution**:
- Ensure authentication middleware is working
- Check `land_owner_audits` table exists
- Verify user authentication in API calls

## Production Checklist

Before going live:

- [ ] Update `.env` with production values
- [ ] Set `APP_ENV=production` and `APP_DEBUG=false`
- [ ] Run `php artisan config:cache`
- [ ] Run `php artisan route:cache`
- [ ] Run `php artisan view:cache`
- [ ] Run `npm run build`
- [ ] Set proper file permissions
- [ ] Create SSL certificate
- [ ] Test all CRUD operations
- [ ] Test authentication
- [ ] Test file uploads
- [ ] Check audit trail functionality

## Performance Optimization

For better production performance:

- [ ] Enable OPcache in PHP
- [ ] Use Redis for cache/sessions
- [ ] Enable Gzip compression
- [ ] Use CDN for static assets
- [ ] Optimize database queries
- [ ] Set up database indexes

## Security Checklist

- [ ] Use HTTPS only
- [ ] Set secure session cookies
- [ ] Update all passwords
- [ ] Remove debug tools
- [ ] Set up proper firewall rules
- [ ] Regular security updates
