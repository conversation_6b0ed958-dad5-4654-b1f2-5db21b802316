<?php
// Fix admin user role assignment

use App\Models\User;
use App\Models\Role;

$adminUser = User::where('email', '<EMAIL>')->first();
$superAdminRole = Role::where('name', 'Super Admin')->first();

if ($adminUser && $superAdminRole) {
    $adminUser->role_id = $superAdminRole->id;
    $adminUser->save();
    echo "Admin user role updated to Super Admin (ID: {$superAdminRole->id})\n";
} else {
    echo "Admin user or Super Admin role not found\n";
    echo "Admin user: " . ($adminUser ? "Found" : "Not found") . "\n";
    echo "Super Admin role: " . ($superAdminRole ? "Found" : "Not found") . "\n";
}

// Also <NAME_EMAIL> user exists, if not create it
if (!$adminUser) {
    $adminUser = User::create([
        'name' => 'Admin User',
        'email' => '<EMAIL>',
        'password' => bcrypt('password'),
        'role_id' => $superAdminRole ? $superAdminRole->id : 1,
        'email_verified_at' => now(),
    ]);
    echo "Created <EMAIL> user\n";
}

echo "Done\n";
