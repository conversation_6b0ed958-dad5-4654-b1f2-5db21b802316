#!/bin/bash
# Production Deployment Script for Real Estate Management
# Run this script on your production server after uploading files

echo "🚀 Starting Production Deployment..."

# 1. Set proper permissions
echo "📝 Setting file permissions..."
sudo chown -R www-data:www-data /path/to/your/project
sudo chmod -R 755 /path/to/your/project
sudo chmod -R 775 /path/to/your/project/storage
sudo chmod -R 775 /path/to/your/project/bootstrap/cache
sudo chmod -R 775 /path/to/your/project/public/landowners

# 2. Install/Update Composer dependencies
echo "📦 Installing Composer dependencies..."
composer install --no-dev --optimize-autoloader

# 3. Install/Update NPM dependencies and build assets
echo "🏗️ Building frontend assets..."
npm ci --production
npm run build

# 4. Environment setup
echo "⚙️ Setting up environment..."
php artisan config:cache
php artisan route:cache
php artisan view:cache

# 5. Database operations
echo "🗄️ Setting up database..."
php artisan migrate --force
php artisan db:seed --force

# 6. Clear and optimize caches
echo "🧹 Optimizing caches..."
php artisan cache:clear
php artisan config:cache
php artisan route:cache
php artisan view:cache
php artisan optimize

# 7. Set up storage link
echo "🔗 Creating storage link..."
php artisan storage:link

# 8. Set final permissions
echo "🔐 Setting final permissions..."
sudo chown -R www-data:www-data storage bootstrap/cache
sudo chmod -R 775 storage bootstrap/cache

echo "✅ Production deployment completed!"
echo "🌐 Your application should now be live at your domain"

# Optional: Create a log entry
echo "$(date): Production deployment completed" >> deployment.log
