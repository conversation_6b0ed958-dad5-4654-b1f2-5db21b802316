# Environment Configuration Guide

This guide explains how to configure the Real Estate Management system for both local development and production environments.

## Local Development Setup

### 1. Environment File (.env)
Your local `.env` file is already configured with:

```bash
APP_NAME="Real Estate Management"
APP_ENV=local
APP_DEBUG=true
APP_URL=http://127.0.0.1:8000

# Database (XAMPP MySQL)
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=react
DB_USERNAME=root
DB_PASSWORD=

# Local CORS Settings
CORS_ALLOWED_ORIGINS=http://127.0.0.1:8000,http://localhost:8000,http://localhost:3000,http://127.0.0.1:3000
SANCTUM_STATEFUL_DOMAINS=127.0.0.1:8000,localhost:8000,localhost:3000
```

### 2. Starting Local Development
```bash
# Clear caches
php artisan config:clear
php artisan route:clear

# Start server
php artisan serve --host=127.0.0.1 --port=8000
```

### 3. Testing Local API
```bash
# Test login
curl -X POST http://127.0.0.1:8000/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"password123"}'

# Test with token
curl -X GET http://127.0.0.1:8000/api/land-owners \
  -H "Authorization: Bearer YOUR_TOKEN"
```

## Production Deployment

### 1. Environment File (.env.production -> .env on server)
Copy `.env.production` to your server as `.env` and update:

```bash
APP_URL=https://yourdomain.com
DB_HOST=localhost
DB_DATABASE=your_production_database
DB_USERNAME=your_production_username
DB_PASSWORD=your_production_password
SANCTUM_STATEFUL_DOMAINS=yourdomain.com,www.yourdomain.com
CORS_ALLOWED_ORIGINS=https://yourdomain.com,https://www.yourdomain.com
```

### 2. Deployment Script
Run the deployment script on your production server:

```bash
chmod +x deploy-production.sh
./deploy-production.sh
```

### 3. Production Diagnostics
Test your production environment:

```bash
# Check system status
curl https://yourdomain.com/api/diagnostics

# Test database
curl https://yourdomain.com/api/test-db

# Test authentication (with valid token)
curl https://yourdomain.com/api/test-auth \
  -H "Authorization: Bearer YOUR_TOKEN"
```

## Key Features Working

✅ **Authentication**: Login/logout with Sanctum tokens
✅ **CRUD Operations**: Create, read, update, delete landowners
✅ **Audit Trail**: Complete user tracking (user_id, name, email)
✅ **CORS**: Proper cross-origin handling
✅ **Environment Aware**: Works on both local and production
✅ **Error Handling**: Proper API error responses

## Environment-Specific Settings

| Setting | Local | Production |
|---------|-------|------------|
| APP_ENV | local | production |
| APP_DEBUG | true | false |
| APP_URL | http://127.0.0.1:8000 | https://yourdomain.com |
| LOG_LEVEL | debug | error |
| SESSION_SECURE_COOKIE | false | true |

## Troubleshooting

### Local Issues
- **Network Error**: Check if server is running on http://127.0.0.1:8000
- **CORS Error**: Verify CORS_ALLOWED_ORIGINS includes your frontend URL
- **Login Fails**: Check database connection and user credentials

### Production Issues
- Run diagnostic endpoints first
- Check server logs in storage/logs/
- Verify .env file has correct database credentials
- Ensure proper file permissions (775 for storage, bootstrap/cache)

## Security Notes

🔒 **Remove diagnostic endpoints** after production deployment
🔒 **Use HTTPS** in production
🔒 **Set strong database passwords**
🔒 **Configure proper SESSION_DOMAIN** for your domain
